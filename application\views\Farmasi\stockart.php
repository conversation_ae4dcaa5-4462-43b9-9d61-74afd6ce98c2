<div class="container-fluid">
    <div class="row">
        <div class="col-12">
            <div class="card">
                <div class="card-header">
                    <h4 class="card-title">Per<PERSON><PERSON><PERSON></h4>
                    <div class="alert alert-info mb-0 mt-2" id="info-nokun" style="display: none;">
                        <small><strong>No. Kunjungan:</strong> <span id="display-nokun"></span></small>
                    </div>
                </div>
                <div class="card-body">
                    <!-- Nav tabs -->
                    <ul class="nav nav-tabs" role="tablist">
                        <?php if ($this->session->userdata('status') != 1) : ?>
                        <li class="nav-item">
                            <a class="nav-link active" data-toggle="tab" href="#formulir" role="tab">
                                <span class="d-block d-sm-none"><i class="fas fa-plus"></i></span>
                                <span class="d-none d-sm-block">Formulir</span>
                            </a>
                        </li>
                        <?php endif ?>
                        <li class="nav-item">
                            <a class="nav-link <?= $this->session->userdata('status') == 1 ? 'active' : '' ?>" data-toggle="tab" href="#history" role="tab">
                                <span class="d-block d-sm-none"><i class="fas fa-history"></i></span>
                                <span class="d-none d-sm-block">History</span>
                            </a>
                        </li>
                    </ul>

                    <!-- Tab panes -->
                    <div class="tab-content p-3 text-muted">
                        <?php if ($this->session->userdata('status') != 1) : ?>
                        <div class="tab-pane active" id="formulir" role="tabpanel">
                            <div class="alert alert-warning" id="warning-nokun" style="display: none;">
                                <i class="fas fa-exclamation-triangle"></i> <strong>Peringatan:</strong> No. Kunjungan tidak ditemukan! Pastikan Anda mengakses halaman ini dari menu pasien yang benar.
                            </div>
                            <div class="row">
                                <div class="col-md-12">
                                    <form id="form-permintaan-stockart">
                                        <div class="card">
                                            <div class="card-header">
                                                <h5>Daftar Item Permintaan</h5>
                                            </div>
                                            <div class="card-body">
                                                <div class="row">
                                                    <div class="col-md-4">
                                                        <div class="form-group">
                                                            <label for="id_item">Pilih Barang <span class="text-danger">*</span></label>
                                                            <select class="form-control select2" id="id_item">
                                                <option value="">-- Pilih Barang --</option>
                                            </select>
                                                        </div>
                                                    </div>
                                                    <div class="col-md-2">
                                                        <div class="form-group">
                                                            <label for="quantity">Jumlah <span class="text-danger">*</span></label>
                                                            <input type="number" class="form-control" id="quantity" min="1">
                                                        </div>
                                                    </div>
                                                    <div class="col-md-4">
                                                        <div class="form-group">
                                                            <label for="note_item">Keterangan Item</label>
                                                            <input type="text" class="form-control" id="note_item" placeholder="Keterangan untuk item ini...">
                                                        </div>
                                                    </div>
                                                    <div class="col-md-2">
                                                        <div class="form-group">
                                                            <label>&nbsp;</label><br>
                                                            <button type="button" class="btn btn-success" id="btn-tambah-item">
                                                                <i class="fa fa-plus"></i> Tambah
                                                            </button>
                                                        </div>
                                                    </div>
                                                </div>
                                                
                                                <div class="table-responsive">
                                                    <table class="table table-bordered" id="table-items">
                                                        <thead>
                                                            <tr>
                                                                <th>No</th>
                                                                <th>Nama Barang</th>
                                                                <th>Jumlah</th>
                                                                <th>Keterangan</th>
                                                                <th>Aksi</th>
                                                            </tr>
                                                        </thead>
                                                        <tbody id="tbody-items">
                                                            <!-- Items will be added here -->
                                                        </tbody>
                                                    </table>
                                                </div>
                                                
                                                <div class="form-group">
                                                    <button type="submit" class="btn btn-primary" id="btn-simpan-permintaan">
                                                        <i class="fa fa-save"></i> Simpan Pengajuan
                                                    </button>
                                                    <button type="button" class="btn btn-secondary" id="btn-reset-form">
                                                        <i class="fa fa-refresh"></i> Reset
                                                    </button>
                                                </div>
                                            </div>
                                        </div>
                                    </form>
                                </div>
                            </div>
                        </div>
                        <?php endif ?>
                        
                        <div class="tab-pane <?= $this->session->userdata('status') == 1 ? 'active' : '' ?>" id="history" role="tabpanel">
                            <div class="table-responsive">
                                <table id="tablePermintaanStockart" class="table table-striped table-bordered table-hover w-100">
                                    <thead class="thead-dark">
                                        <tr>
                                            <th style="width: 5%;" class="text-center">No</th>
                                            <th class="text-center">ID</th>
                                            <th class="text-center">Jumlah</th>
                                            <th class="text-center">Status</th>
                                            <th class="text-center">Dibuat</th>
                                            <th class="text-center">Diubah</th>
                                            <th class="text-center">Aksi</th>
                                        </tr>
                                    </thead>
                                    <tbody>
                                    </tbody>
                                </table>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Modal Edit Permintaan -->
<div class="modal fade" id="modal-edit-permintaan" tabindex="-1" role="dialog" aria-labelledby="modal-edit-permintaan-label" aria-hidden="true">
    <div class="modal-dialog modal-lg" role="document">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="modal-edit-permintaan-label">Edit Permintaan Stockart</h5>
                <button type="button" class="close" data-dismiss="modal" aria-label="Close">
                    <span aria-hidden="true">&times;</span>
                </button>
            </div>
            <form id="form-edit-permintaan">
                <div class="modal-body">
                    <input type="hidden" id="edit-permintaan-id" name="id">
                    
                    <div class="form-group">
                        <label for="edit-id_item">Pilih Barang <span class="text-danger">*</span></label>
                        <select class="form-control" id="edit-id_item" name="id_item" required>
                            <option value="">-- Pilih Barang --</option>
                        </select>
                    </div>
                    
                    <div class="form-group">
                        <label for="edit-quantity">Jumlah <span class="text-danger">*</span></label>
                        <input type="number" class="form-control" id="edit-quantity" name="quantity" min="1" required>
                    </div>
                    
                    <div class="form-group">
                        <label for="edit-note">Keterangan</label>
                        <textarea class="form-control" id="edit-note" name="note" rows="3"></textarea>
                    </div>
                    
                    <div class="form-group">
                        <label for="edit-status">Status</label>
                        <select class="form-control" id="edit-status" name="status">
                            <option value="1">Diajukan</option>
                            <option value="2">Diterima</option>
                            <option value="0">Ditolak</option>
                        </select>
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-dismiss="modal">Tutup</button>
                    <button type="submit" class="btn btn-primary">Update</button>
                </div>
            </form>
        </div>
    </div>
</div>

<script>
$(document).ready(function() {
    // Get parameters from controller
    const nomr = '<?= $nomr ?>';
    const nokun = '<?= $nokun ?>';
    
    // Display nokun info
    if(nokun && nokun.trim() !== '') {
        $('#display-nokun').text(nokun);
        $('#info-nokun').show();
        $('#warning-nokun').hide();
    } else {
        $('#info-nokun').hide();
        $('#warning-nokun').show();
    }
    
    // Array to store items
    var items = [];
    
    // Load items for dropdown
    loadBarangDropdown();
    
    // Initialize DataTable for history
    var table = $('#tablePermintaanStockart').DataTable({
        "processing": true,
        "serverSide": true,
        "searching": true,
        "ordering": true,
        "order": [[5, "desc"]],
        "ajax": {
            "url": "<?= base_url('Farmasi/Stockart/getData') ?>",
            "type": "POST",
            "data": function(d) {
                d.nokun = nokun;
            },
            "error": function(xhr, error, code) {
                console.log('DataTables Ajax Error:', error);
                console.log('Response:', xhr.responseText);
                showModal('error', 'Error!', 'Terjadi kesalahan saat memuat data: ' + error);
            }
        },
        "columns": [
            { 
                "data": null, 
                "className": "text-center",
                "orderable": false,
                "render": function(data, type, row, meta) { return meta.row + 1; } 
            },
            { 
                "data": "id_pengajuan",
                "name": "id_pengajuan",
                "orderable": true,
                "render": function(data, type, row) {
                    return (data && data.toString().trim() !== '') ? data : '-';
                }
            },
            { 
                "data": "jumlah_item",
                "name": "jumlah_item",
                "className": "text-center",
                "orderable": true,
                "render": function(data, type, row) {
                    return (data && data.toString().trim() !== '') ? data : '0';
                }
            },
            { 
                "data": "status_text",
                "name": "status_text",
                "className": "text-center",
                "orderable": true,
                "render": function(data, type, row) {
                    var statusText = data || '-';
                    var badgeClass = '';
                    switch(statusText) {
                        case 'Diajukan': 
                            badgeClass = 'badge-warning'; 
                            break;
                        case 'Diproses': 
                            badgeClass = 'badge-info'; 
                            break;
                        case 'Diterima': 
                            badgeClass = 'badge-success'; 
                            break;
                        case 'Ditolak': 
                            badgeClass = 'badge-danger'; 
                            break;
                        default: 
                            badgeClass = 'badge-secondary';
                    }
                    return '<span class="badge ' + badgeClass + '">' + statusText + '</span>';
                }
            },
            { 
                "data": "created_at",
                "name": "created_at",
                "className": "text-center",
                "orderable": true,
                "render": function(data, type, row) {
                    var createdAt = (row.created_at && row.created_at.toString().trim() !== '') ? row.created_at : '-';
                    var createdBy = (row.created_by_name && row.created_by_name.toString().trim() !== '') ? row.created_by_name : '-';
                    return createdAt + ' / ' + createdBy;
                }
            },
            { 
                "data": "updated_at",
                "name": "updated_at",
                "className": "text-center",
                "orderable": true,
                "render": function(data, type, row) {
                    var updatedAt = (row.updated_at && row.updated_at.toString().trim() !== '') ? row.updated_at : '-';
                    var updatedBy = (row.updated_by_name && row.updated_by_name.toString().trim() !== '') ? row.updated_by_name : '-';
                    return updatedAt + ' / ' + updatedBy;
                }
            },
            {
                "data": null,
                "className": "text-center",
                "orderable": false,
                "render": function(data, type, row) {
                    var buttons = '<div class="btn-group" role="group">' +
                                 '<button class="btn btn-sm btn-info btn-detail" data-id="' + row.id_pengajuan + '" title="Detail"><i class="fa fa-eye"></i></button>';
                    
                    // Tampilkan tombol hapus hanya jika status bukan 2 (diterima)
                    if(parseInt(row.status) !== 2) {
                        buttons += '<button class="btn btn-sm btn-danger btn-hapus" data-id="' + row.id_pengajuan + '" title="Hapus"><i class="fa fa-trash"></i></button>';
                    }
                    
                    buttons += '</div>';
                    return buttons;
                }
            }
        ],
        "language": {
            "processing": "Memuat data...",
            "lengthMenu": "Tampilkan _MENU_ data per halaman",
            "zeroRecords": "Data tidak ditemukan",
            "info": "Menampilkan _START_ sampai _END_ dari _TOTAL_ data",
            "infoEmpty": "Menampilkan 0 sampai 0 dari 0 data",
            "infoFiltered": "",
            "search": "Cari:",
            "paginate": {
                "first": "Pertama",
                "last": "Terakhir",
                "next": "Selanjutnya",
                "previous": "Sebelumnya"
            }
        }
    });
    
    // Load barang dropdown
    function loadBarangDropdown() {
        $('#id_item').select2({
            placeholder: 'Ketik untuk mencari barang...',
            allowClear: true,
            ajax: {
                url: '<?= base_url("Farmasi/Stockart/getBarang") ?>',
                dataType: 'json',
                delay: 250,
                data: function (params) {
                    return {
                        q: params.term // search term
                    };
                },
                processResults: function (data) {
                    return {
                        results: data
                    };
                },
                cache: true
            },
            minimumInputLength: 0
        });
        
        // Load edit dropdown normally
        $.ajax({
            url: '<?= base_url("Farmasi/Stockart/getBarang") ?>',
            type: 'GET',
            dataType: 'json',
            success: function(response) {
                var options = '<option value="">-- Pilih Barang --</option>';
                $.each(response, function(index, item) {
                    options += '<option value="' + item.id + '">' + item.text + '</option>';
                });
                $('#edit-id_item').html(options);
            },
            error: function() {
                showModal('error', 'Error!', 'Gagal memuat data barang');
            }
        });
    }
    
    // Add item to list
    $('#btn-tambah-item').on('click', function() {
        var id_item = $('#id_item').val();
        var nama_barang = $('#id_item option:selected').text();
        var quantity = $('#quantity').val();
        var note_item = $('#note_item').val();
        
        if(!id_item || !quantity) {
            showModal('warning', 'Peringatan!', 'Pilih barang dan masukkan jumlah!');
            return;
        }
        
        // Check if item already exists
        var existingIndex = items.findIndex(item => item.id_item === id_item);
        if(existingIndex !== -1) {
            // Update existing item
            items[existingIndex].quantity = parseInt(items[existingIndex].quantity) + parseInt(quantity);
            items[existingIndex].note_item = note_item;
        } else {
            // Add new item
            items.push({
                id_item: id_item,
                nama_barang: nama_barang,
                quantity: quantity,
                note_item: note_item
            });
        }
        
        updateItemsTable();
        
        // Reset item form
        $('#id_item').val(null).trigger('change');
        $('#quantity').val('');
        $('#note_item').val('');
    });
    
    // Remove item from list
    $(document).on('click', '.btn-remove-item', function() {
        var index = $(this).data('index');
        items.splice(index, 1);
        updateItemsTable();
    });
    
    // Update items table
    function updateItemsTable() {
        var tbody = $('#tbody-items');
        tbody.empty();
        
        items.forEach(function(item, index) {
            var row = '<tr>' +
                '<td>' + (index + 1) + '</td>' +
                '<td>' + item.nama_barang + '</td>' +
                '<td>' + item.quantity + '</td>' +
                '<td>' + (item.note_item || '-') + '</td>' +
                '<td><button type="button" class="btn btn-sm btn-danger btn-remove-item" data-index="' + index + '"><i class="fa fa-trash"></i></button></td>' +
                '</tr>';
            tbody.append(row);
        });
    }
    
    // Form submission for new request
    $('#form-permintaan-stockart').on('submit', function(e) {
        e.preventDefault();
        
        if(!nokun || nokun.trim() === '') {
            showModal('warning', 'Peringatan!', 'No. Kunjungan tidak ditemukan! Pastikan Anda mengakses halaman ini dari menu yang benar.');
            return;
        }
        
        if(items.length === 0) {
            showModal('warning', 'Peringatan!', 'Tambahkan minimal 1 item!');
            return;
        }
        
        var formData = {
            nokun: nokun,
            items: items
        };
        
        $.ajax({
            url: '<?= base_url("Farmasi/Stockart/simpan") ?>',
            type: 'POST',
            data: formData,
            dataType: 'json',
            success: function(response) {
                if(response.status === 'success') {
                    showModal('success', 'Berhasil!', 'Pengajuan berhasil disimpan!', function() {
                        $('#form-permintaan-stockart')[0].reset();
                        $('#id_item').val(null).trigger('change');
                        items = [];
                        updateItemsTable();
                        table.ajax.reload();
                        // Switch to history tab
                        $('a[href="#history"]').tab('show');
                    });
                } else {
                    showModal('error', 'Gagal!', 'Gagal menyimpan permintaan: ' + response.message);
                }
            },
            error: function() {
                showModal('error', 'Error!', 'Terjadi kesalahan saat menyimpan data');
            }
        });
    });
    
    // Reset form
    $('#btn-reset-form').click(function() {
        // Reset form HTML
        $('#form-permintaan-stockart')[0].reset();
        
        // Reset select2
        $('#id_item').val(null).trigger('change');
        
        // Clear items array
        items = [];
        
        // Update table
        updateItemsTable();
        
        // Clear individual form fields
        $('#quantity').val('');
        $('#note_item').val('');
    });
    
    // Detail button click
    $(document).on('click', '.btn-detail', function() {
        var id_pengajuan = $(this).data('id');
        
        $.ajax({
            url: '<?= base_url("Farmasi/Stockart/getDetailPengajuan") ?>',
            type: 'GET',
            data: { id_pengajuan: id_pengajuan },
            dataType: 'json',
            success: function(response) {
                if(response.status === 'success') {
                    var modalBody = $('#modalDetail .modal-body');
                    var html = '<div class="row">';
                    
                    // Informasi Pengajuan
                    html += '<div class="col-md-6">';
                    html += '<h6><strong>Informasi Pengajuan</strong></h6>';
                    html += '<table class="table table-sm table-borderless">';
                    html += '<tr><td><strong>ID Pengajuan:</strong></td><td>' + (response.data.id_pengajuan && response.data.id_pengajuan.toString().trim() !== '' ? response.data.id_pengajuan : '-') + '</td></tr>';
                    html += '<tr><td><strong>No. Kunjungan:</strong></td><td>' + (response.data.nokun && response.data.nokun.toString().trim() !== '' ? response.data.nokun : '-') + '</td></tr>';
                    html += '<tr><td><strong>No. Order Resep:</strong></td><td>' + (response.data.ref && response.data.ref.toString().trim() !== '' && response.data.ref !== '-' ? response.data.ref : '-') + '</td></tr>';
                    html += '<tr><td><strong>Status:</strong></td><td>';
                    
                    var statusText = response.data.status_text || '-';
                    var badgeClass = '';
                    switch(statusText) {
                        case 'Diajukan': 
                            badgeClass = 'badge-warning'; 
                            break;
                        case 'Diproses': 
                            badgeClass = 'badge-info'; 
                            break;
                        case 'Diterima': 
                            badgeClass = 'badge-success'; 
                            break;
                        case 'Ditolak': 
                            badgeClass = 'badge-danger'; 
                            break;
                        default: 
                            badgeClass = 'badge-secondary';
                    }
                    html += '<span class="badge ' + badgeClass + '">' + statusText + '</span></td></tr>';
                    html += '</table>';
                    html += '</div>';
                    
                    // Informasi Audit
                    html += '<div class="col-md-6">';
                    html += '<h6><strong>Informasi Audit</strong></h6>';
                    html += '<table class="table table-sm table-borderless">';
                    html += '<tr><td><strong>Tanggal Dibuat:</strong></td><td>' + (response.data.created_at && response.data.created_at.toString().trim() !== '' ? response.data.created_at : '-') + '</td></tr>';
                    html += '<tr><td><strong>Dibuat Oleh:</strong></td><td>' + (response.data.created_by_name && response.data.created_by_name.toString().trim() !== '' ? response.data.created_by_name : '-') + '</td></tr>';
                    html += '<tr><td><strong>Tanggal Diubah:</strong></td><td>' + (response.data.updated_at && response.data.updated_at.toString().trim() !== '' ? response.data.updated_at : '-') + '</td></tr>';
                    html += '<tr><td><strong>Diubah Oleh:</strong></td><td>' + (response.data.updated_by_name && response.data.updated_by_name.toString().trim() !== '' ? response.data.updated_by_name : '-') + '</td></tr>';
                    html += '</table>';
                    html += '</div>';
                    html += '</div>';
                    
                    html += '<hr>';
                    html += '<h6><strong>Daftar Item:</strong></h6>';
                    html += '<div class="table-responsive">';
                    html += '<table class="table table-sm table-bordered">';
                    html += '<thead class="thead-light"><tr><th class="text-center">No</th><th>Nama Barang</th><th class="text-center">Jumlah</th><th>Catatan Item</th></tr></thead>';
                    html += '<tbody>';
                    
                    if(response.data.items && response.data.items.length > 0) {
                        response.data.items.forEach(function(item, index) {
                            html += '<tr>';
                            html += '<td class="text-center">' + (index + 1) + '</td>';
                            html += '<td>' + (item.nama_barang && item.nama_barang.toString().trim() !== '' ? item.nama_barang : '-') + '</td>';
                            html += '<td class="text-center">' + (item.quantity && item.quantity.toString().trim() !== '' ? item.quantity : '0') + '</td>';
                            html += '<td>' + (item.note_item && item.note_item.toString().trim() !== '' ? item.note_item : '-') + '</td>';
                            html += '</tr>';
                        });
                    } else {
                        html += '<tr><td colspan="4" class="text-center">Tidak ada item</td></tr>';
                    }
                    
                    html += '</tbody></table>';
                    html += '</div>';
                    modalBody.html(html);
                    
                    // Update modal footer with Order Resep button if conditions are met
                    var modalFooter = $('#modalDetail .modal-footer');
                    var userStatus = '<?= $this->session->userdata("status") ?>';
                    
                    // Clear existing buttons except close button
                    modalFooter.find('.btn-success').remove();
                    
                    // Add Order Resep button if user is doctor (status = 1) and stockart status is 1 (diajukan)
                    if (userStatus == '1' && parseInt(response.data.status) == 1) {
                        var orderResepBtn = '<button type="button" class="btn btn-success mr-2" id="btn-order-resep" data-id="' + response.data.id_pengajuan + '" data-nokun="' + response.data.nokun + '"><i class="fa fa-file-medical"></i> Order Resep</button>';
                        modalFooter.prepend(orderResepBtn);
                    }
                    
                    $('#modalDetail').modal('show');
                } else {
                    showModal('error', 'Error!', 'Error: ' + response.message);
                }
            },
            error: function() {
                showModal('error', 'Error!', 'Terjadi kesalahan saat mengambil data');
            }
        });
    });
    
    // Order Resep button click
    $(document).on('click', '#btn-order-resep', function() {
        var id_pengajuan = $(this).data('id');
        var nokun = $(this).data('nokun');
        
        showConfirmModal('Konfirmasi Order Resep', 'Apakah Anda yakin ingin membuat order resep dari pengajuan stockart ini?', function() {
            $.ajax({
                url: '<?= base_url("Farmasi/Stockart/orderResep") ?>',
                type: 'POST',
                data: { 
                    id_pengajuan: id_pengajuan,
                    nokun: nokun
                },
                dataType: 'json',
                success: function(response) {
                    if(response.status === 'success') {
                        showModal('success', 'Berhasil!', response.message, function() {
                            $('#modalDetail').modal('hide');
                            table.ajax.reload();
                        });
                    } else {
                        showModal('error', 'Error!', 'Error: ' + response.message);
                    }
                },
                error: function() {
                    showModal('error', 'Error!', 'Terjadi kesalahan saat membuat order resep');
                }
            });
        });
    });
    
    // Delete button click
    $(document).on('click', '.btn-hapus', function() {
        var id_pengajuan = $(this).data('id');
        
        showConfirmModal('Konfirmasi Hapus', 'Apakah Anda yakin ingin menghapus pengajuan ini? Semua item dalam pengajuan akan ikut terhapus.', function() {
                $.ajax({
                    url: '<?= base_url("Farmasi/Stockart/hapusPengajuan") ?>',
                    type: 'POST',
                    data: { id_pengajuan: id_pengajuan },
                    dataType: 'json',
                    success: function(response) {
                        if(response.status === 'success') {
                            showModal('success', 'Berhasil!', 'Pengajuan berhasil dihapus!', function() {
                                table.ajax.reload();
                            });
                        } else {
                            showModal('error', 'Error!', 'Error: ' + response.message);
                        }
                    },
                    error: function() {
                        showModal('error', 'Error!', 'Terjadi kesalahan saat menghapus data');
                    }
                });
        });
    });

    // Helper functions to replace SweetAlert with Bootstrap modals
    function showModal(type, title, message, callback) {
        var iconClass = '';
        var modalClass = '';
        
        switch(type) {
            case 'success':
                iconClass = 'fa-check-circle text-success';
                modalClass = 'border-success';
                break;
            case 'error':
                iconClass = 'fa-times-circle text-danger';
                modalClass = 'border-danger';
                break;
            case 'warning':
                iconClass = 'fa-exclamation-triangle text-warning';
                modalClass = 'border-warning';
                break;
            case 'info':
                iconClass = 'fa-info-circle text-info';
                modalClass = 'border-info';
                break;
            default:
                iconClass = 'fa-info-circle text-info';
                modalClass = 'border-info';
        }
        
        $('#alertModal .modal-content').removeClass('border-success border-danger border-warning border-info').addClass(modalClass);
        $('#alertModal .modal-title').html('<i class="fa ' + iconClass + '"></i> ' + title);
        $('#alertModal .modal-body').text(message);
        
        // Remove any existing event handlers
        $('#alertModal .btn-primary').off('click');
        
        // Add callback if provided
        if(callback && typeof callback === 'function') {
            $('#alertModal .btn-primary').on('click', function() {
                $('#alertModal').modal('hide');
                callback();
            });
        }
        
        $('#alertModal').modal('show');
    }
    
    function showConfirmModal(title, message, confirmCallback, cancelCallback) {
        $('#confirmModal .modal-title').html('<i class="fa fa-question-circle text-warning"></i> ' + title);
        $('#confirmModal .modal-body').text(message);
        
        // Remove any existing event handlers
        $('#btnKonfirmasi').off('click');
        $('#confirmModal .btn-secondary').off('click');
        
        // Add confirm callback
        if(confirmCallback && typeof confirmCallback === 'function') {
            $('#btnKonfirmasi').on('click', function() {
                $('#confirmModal').modal('hide');
                confirmCallback();
            });
        }
        
        // Add cancel callback
        if(cancelCallback && typeof cancelCallback === 'function') {
            $('#confirmModal .btn-secondary').on('click', function() {
                $('#confirmModal').modal('hide');
                cancelCallback();
            });
        }
        
        $('#confirmModal').modal('show');
    }
});
</script>

<style>
/* Custom styling for table */
#tablePermintaanStockart {
    font-size: 12px;
    width: 100% !important;
    table-layout: fixed;
}

#tablePermintaanStockart th {
    vertical-align: middle;
    white-space: nowrap;
    font-weight: 600;
    font-size: 11px;
}

#tablePermintaanStockart td {
    vertical-align: middle;
    word-wrap: break-word;
    font-size: 11px;
}

#tablePermintaanStockart .btn {
    margin: 1px;
    padding: 0.2rem 0.4rem;
    font-size: 0.7rem;
}

/* Button group styling */
#tablePermintaanStockart .btn-group .btn {
    margin: 0;
    border-radius: 0;
}

#tablePermintaanStockart .btn-group .btn:first-child {
    border-top-left-radius: 0.25rem;
    border-bottom-left-radius: 0.25rem;
}

#tablePermintaanStockart .btn-group .btn:last-child {
    border-top-right-radius: 0.25rem;
    border-bottom-right-radius: 0.25rem;
}

#tablePermintaanStockart .btn-group {
    display: inline-flex;
}

/* Ensure table takes full width */
.table-responsive {
    width: 100%;
    overflow-x: auto;
}

#history .table-responsive {
    margin: 0;
    padding: 0;
}

/* DataTables wrapper full width */
.dataTables_wrapper {
    width: 100% !important;
}

/* Column specific styling */
#tablePermintaanStockart th:nth-child(1),
#tablePermintaanStockart td:nth-child(1) {
    text-align: center;
    width: 5%;
}

#tablePermintaanStockart th:nth-child(2),
#tablePermintaanStockart td:nth-child(2) {
    width: 15%;
}

#tablePermintaanStockart th:nth-child(3),
#tablePermintaanStockart td:nth-child(3) {
    text-align: center;
    width: 10%;
}

#tablePermintaanStockart th:nth-child(4),
#tablePermintaanStockart td:nth-child(4) {
    text-align: center;
    width: 10%;
}

#tablePermintaanStockart th:nth-child(5),
#tablePermintaanStockart td:nth-child(5) {
    text-align: center;
    width: 20%;
}

#tablePermintaanStockart th:nth-child(6),
#tablePermintaanStockart td:nth-child(6) {
    text-align: center;
    width: 20%;
}

#tablePermintaanStockart th:nth-child(7),
#tablePermintaanStockart td:nth-child(7) {
    text-align: center;
    width: 20%;
}

/* Responsive adjustments */
@media (max-width: 768px) {
    #tablePermintaanStockart {
        font-size: 10px;
    }
    
    #tablePermintaanStockart th {
        font-size: 9px;
    }
    
    #tablePermintaanStockart td {
        font-size: 9px;
    }
    
    #tablePermintaanStockart .btn {
        padding: 0.15rem 0.3rem;
        font-size: 0.6rem;
    }
}

/* Badge styling */
.badge {
    font-size: 0.85em;
    padding: 0.25em 0.5em;
}
</style>

<!-- Modal Detail Pengajuan -->
<div class="modal fade" id="modalDetail" tabindex="-1" role="dialog" aria-labelledby="modalDetailLabel" aria-hidden="true">
    <div class="modal-dialog modal-lg" role="document">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="modalDetailLabel">Detail Pengajuan Stockart</h5>
                <button type="button" class="close" data-dismiss="modal" aria-label="Close">
                    <span aria-hidden="true">&times;</span>
                </button>
            </div>
            <div class="modal-body">
                <!-- Detail content will be loaded here -->
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-dismiss="modal">Tutup</button>
            </div>
        </div>
    </div>
</div>

<!-- Alert Modal -->
<div class="modal fade" id="alertModal" tabindex="-1" role="dialog" aria-labelledby="alertModalLabel" aria-hidden="true">
    <div class="modal-dialog" role="document">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="alertModalLabel">Alert</h5>
                <button type="button" class="close" data-dismiss="modal" aria-label="Close">
                    <span aria-hidden="true">&times;</span>
                </button>
            </div>
            <div class="modal-body">
                <!-- Message will be inserted here -->
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-primary" data-dismiss="modal">OK</button>
            </div>
        </div>
    </div>
</div>

<!-- Confirm Modal -->
<div class="modal fade" id="confirmModal" tabindex="-1" role="dialog" aria-labelledby="confirmModalLabel" aria-hidden="true">
    <div class="modal-dialog" role="document">
        <div class="modal-content border-warning">
            <div class="modal-header">
                <h5 class="modal-title" id="confirmModalLabel">Konfirmasi</h5>
                <button type="button" class="close" data-dismiss="modal" aria-label="Close">
                    <span aria-hidden="true">&times;</span>
                </button>
            </div>
            <div class="modal-body">
                <!-- Message will be inserted here -->
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-dismiss="modal">Batal</button>
                <button type="button" id="btnKonfirmasi" class="btn btn-success">Konfirmasi</button>
            </div>
        </div>
    </div>
</div>